spring:
  datasource:
    url: jdbc:aws-wrapper:mysql://mysql.t2.moego.dev:40106/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: moego_developer_240310_eff7a0dc
    password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
moego:
  data-sources:
    - name: reader
      url: jdbc:aws-wrapper:mysql://mysql.t2.moego.dev:40106/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: moego_developer_240310_eff7a0dc
      password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
  posthog:
    api-key: phc_hH0zrnA2tp7s5Vyv1ktl4Ht9exe6UQfPbUvv1ORgv80
    api-host: https://app.posthog.com
  invite-staff:
    sign-in: https://go.t2.moego.dev/sign_in?inviteCode={0}
    sign-up: https://go.t2.moego.dev/sign_up?inviteCode={0}
logging:
  level:
    com.moego.server.business.mapper: debug

s3:
  region: us-west-2
  key: ********************
  secret: C68h5Eb7ZaP/TqD11+OGc2NsTTpMXYoq+7PZiAiR