package com.moego.server.business.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.common.enums.StaffEnum;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapperbean.MoeStaff;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@Transactional
@TestPropertySource(locations = "classpath:application-local.yml")
@ActiveProfiles("local")
public class LastBusinessTest {

    @Autowired
    private MoeStaffMapper staffMapper;

    @Autowired
    private StaffService staffService;

    private MoeStaff assertBusinessIs(int accountId, int businessId, boolean refreshAgain) {
        var staff = staffService.getLastVisitedStaffByAccount(accountId);
        // 是指定 business 的 staff
        assertThat(staff.getBusinessId()).isEqualTo(businessId);
        if (refreshAgain) {
            staffService.updateAccountLastVisitedAt(staff.getId());
        }
        // 再取一次，还是这个 staff
        var staffAgain = staffService.getLastVisitedStaffByAccount(accountId);
        assertThat(staff.getId()).isEqualTo(staffAgain.getId());
        assertThat(staff.getBusinessId()).isEqualTo(staffAgain.getBusinessId());
        assertThat(staff.getAccountId()).isEqualTo(staffAgain.getAccountId());
        assertThat(staff.getStatus()).isEqualTo(staffAgain.getStatus());
        assertThat(staff.getAllowLogin()).isEqualTo(staffAgain.getAllowLogin());

        if (refreshAgain) {
            assertThat(staff.getAccountLastVisitedAt()).isLessThan(staffAgain.getAccountLastVisitedAt());
        } else {
            assertThat(staff.getAccountLastVisitedAt()).isEqualTo(staffAgain.getAccountLastVisitedAt());
        }

        return staff;
    }

    @Test
    public void test() {
        var accountId = 1;

        var businessIdList = List.of(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        Map<Integer, Integer> staffIdMap = new HashMap<>();
        // 初始化 staff, 全都是 status = 1 和 allow login = 1
        for (var i : businessIdList) {
            MoeStaff staff = new MoeStaff();
            staff.setAccountId(accountId);

            staff.setBusinessId(i);
            staff.setFirstName(String.valueOf(i));
            staff.setLastName(String.valueOf(i));
            staff.setStatus(StaffEnum.STATUS_NORMAL);
            staff.setAllowLogin(StaffEnum.ALLOW_LOGIN_TRUE);

            staffMapper.insertSelective(staff);
            staffIdMap.put(i, staff.getId());
        }
        assertThat(staffService.getOrderedStaffForAccount(accountId)).hasSize(10);

        // case 1: 从未访问过，默认取第 1 个 business 的 staff
        assertBusinessIs(accountId, 1, false);

        // case 2: 切换到另一个 business, 成功
        var staff = staffService.switchBusiness(accountId, 7);
        assertThat(staff.getId()).isEqualTo(staffIdMap.get(7));
        assertBusinessIs(accountId, 7, false);

        // 再切一次，成功
        staff = staffService.switchBusiness(accountId, 6);
        assertThat(staff.getId()).isEqualTo(staffIdMap.get(6));
        assertBusinessIs(accountId, 6, false);

        // 再切一次，成功
        staff = staffService.switchBusiness(accountId, 5);
        assertThat(staff.getId()).isEqualTo(staffIdMap.get(5));
        assertBusinessIs(accountId, 5, false);

        // case 3: 切换到没有绑定的 business, 会失败
        staff = staffService.switchBusiness(accountId, 11);
        assertThat(staff).isNull();
        // 仍然是上次访问的 business 5
        staff = assertBusinessIs(accountId, 5, false);

        // case 4: 删除 staff (business 5)
        MoeStaff condition = new MoeStaff();
        condition.setId(staff.getId());
        condition.setStatus(StaffEnum.STATUS_DELETE);
        staffMapper.updateByPrimaryKeySelective(condition);
        // 再查询 last visited 时会切到 上上次 访问的 business 6
        assertBusinessIs(accountId, 6, false);

        // case 5: 不能切到删除的 staff 所在的 business
        staff = staffService.switchBusiness(accountId, 5);
        assertThat(staff).isNull();
        // 依然是 上上次 访问的 business 6
        staff = assertBusinessIs(accountId, 6, false);

        // case 6: 将 staff (business 6) 设置为 not allow login
        condition = new MoeStaff();
        condition.setId(staff.getId());
        condition.setStatus(StaffEnum.ALLOW_LOGIN_FALSE);
        staffMapper.updateByPrimaryKeySelective(condition);
        // 再查询 last visited 时会切到 上上上次 访问的 business 7
        assertBusinessIs(accountId, 7, false);

        // case 7: 不能切到 not allow login 的 staff 所在的 business
        staff = staffService.switchBusiness(accountId, 6);
        assertThat(staff).isNull();
        // 仍然是上次访问的 business 7
        assertBusinessIs(accountId, 7, false);

        // case 8: refresh
        assertBusinessIs(accountId, 7, true);
    }
}
