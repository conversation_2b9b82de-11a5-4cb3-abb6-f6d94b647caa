package com.moego.server.business.service;

import static com.moego.server.business.common.consts.DataSourceConst.READER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.business.web.vo.StaffByWorkingDateVO;
import com.moego.server.business.web.vo.StaffWithWorkingRangeVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * WorkingDailyService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class WorkingDailyServiceTest {

    @InjectMocks
    private WorkingDailyService workingDailyService;

    @Mock
    private StaffWorkingHourService staffWorkingHourService;

    @Mock
    private MoeStaffMapper moeStaffMapper;

    /**
     * 测试 simpleQueryStaffWorkTime 方法的排序功能
     * 验证返回结果按照 sort 降序、staffId 升序排列
     */
    @Test
    void testSimpleQueryStaffWorkTime_SortOrder() {
        // 准备测试数据
        int businessId = 1;
        Integer tokenStaffId = 100;
        WorkingDailyQueryRangeVo rangeVo = new WorkingDailyQueryRangeVo()
                .setStartDate("2025-01-01")
                .setEndDate("2025-01-07")
                .setStaffIdList(List.of(1, 2, 3, 4));

        // Mock staffWorkingHourService.getStaffListByWorkingDateAndStaffIdList 方法的返回值
        List<StaffByWorkingDateVO> staffResults = new ArrayList<>();
        var staffByWorkingDateVO1 = new StaffByWorkingDateVO();
        staffByWorkingDateVO1.setDate("2025-01-01");
        staffByWorkingDateVO1.setStaffs(List.of(
                new StaffWithWorkingRangeVO(1, List.of(new TimeRangeDto(900, 1700))),
                new StaffWithWorkingRangeVO(2, List.of(new TimeRangeDto(900, 1700))),
                new StaffWithWorkingRangeVO(3, List.of(new TimeRangeDto(900, 1700))),
                new StaffWithWorkingRangeVO(4, List.of(new TimeRangeDto(900, 1700)))));
        staffResults.add(staffByWorkingDateVO1);
        when(staffWorkingHourService.getStaffListByWorkingDateAndStaffIdList(
                        businessId, List.of(1, 2, 3, 4), "2025-01-01", "2025-01-07"))
                .thenReturn(staffResults);

        // Mock moeStaffMapper.useDataSource(READER).queryStaffByIdListV2 方法
        List<MoeStaff> staffList = new ArrayList<>();
        var moeStaff1 = new MoeStaff();
        moeStaff1.setId(1);
        moeStaff1.setSort(10);
        moeStaff1.setFirstName("Alice");
        moeStaff1.setLastName("Smith");
        staffList.add(moeStaff1);
        var moeStaff2 = new MoeStaff();
        moeStaff2.setId(2);
        moeStaff2.setSort(20);
        moeStaff2.setFirstName("Bob");
        moeStaff2.setLastName("Johnson");
        staffList.add(moeStaff2);
        var moeStaff3 = new MoeStaff();
        moeStaff3.setId(3);
        moeStaff3.setSort(20);
        moeStaff3.setFirstName("Charlie");
        moeStaff3.setLastName("Brown");
        staffList.add(moeStaff3);
        var moeStaff4 = new MoeStaff();
        moeStaff4.setId(4);
        moeStaff4.setSort(30);
        moeStaff4.setFirstName("David");
        moeStaff4.setLastName("Wilson");
        staffList.add(moeStaff4);

        // 创建一个新的 mock 对象来代表 useDataSource 的返回值
        MoeStaffMapper readerMapper = mock(MoeStaffMapper.class);
        when(moeStaffMapper.useDataSource(READER)).thenReturn(readerMapper);
        when(readerMapper.queryStaffByIdListV2(List.of(1, 2, 3, 4))).thenReturn(staffList);

        // 执行测试
        List<StaffWorkingRangeDto> result =
                workingDailyService.simpleQueryStaffWorkTime(businessId, tokenStaffId, rangeVo);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size());

        // 验证排序：按 sort 降序，然后按 staffId 升序
        // 期望顺序：staff4(sort=30,id=4) -> staff2(sort=20,id=2) -> staff3(sort=20,id=3) -> staff1(sort=10,id=1)
        assertEquals(4, result.get(0).getStaffId()); // sort=30, staffId=4
        assertEquals(30, result.get(0).getSort());

        assertEquals(2, result.get(1).getStaffId()); // sort=20, staffId=2 (较小的staffId)
        assertEquals(20, result.get(1).getSort());

        assertEquals(3, result.get(2).getStaffId()); // sort=20, staffId=3 (较大的staffId)
        assertEquals(20, result.get(2).getSort());

        assertEquals(1, result.get(3).getStaffId()); // sort=10, staffId=1
        assertEquals(10, result.get(3).getSort());

        // 验证其他字段也正确设置
        assertEquals("David", result.get(0).getFirstName());
        assertEquals("Wilson", result.get(0).getLastName());
        assertEquals("#FF0000", result.get(0).getColorCode());
        assertNotNull(result.get(0).getTimeRange());
    }

    /**
     * 测试相同 sort 值的员工按 staffId 升序排列
     */
    @Test
    void testSimpleQueryStaffWorkTime_SameSortDifferentStaffId() {
        // 准备测试数据
        int businessId = 1;
        Integer tokenStaffId = 100;
        var staffIdList = List.of(1, 3, 5);
        WorkingDailyQueryRangeVo rangeVo = new WorkingDailyQueryRangeVo()
                .setStartDate("2025-01-01")
                .setEndDate("2025-01-07")
                .setStaffIdList(staffIdList);

        List<StaffByWorkingDateVO> staffResults = new ArrayList<>();
        var staffByWorkingDateVO1 = new StaffByWorkingDateVO();
        staffByWorkingDateVO1.setDate("2025-01-01");
        staffByWorkingDateVO1.setStaffs(List.of(
                new StaffWithWorkingRangeVO(1, List.of(new TimeRangeDto(540, 1020))),
                new StaffWithWorkingRangeVO(3, List.of(new TimeRangeDto(540, 1020))),
                new StaffWithWorkingRangeVO(5, List.of(new TimeRangeDto(540, 1020)))));
        staffResults.add(staffByWorkingDateVO1);
        when(staffWorkingHourService.getStaffListByWorkingDateAndStaffIdList(
                        businessId, staffIdList, "2025-01-01", "2025-01-07"))
                .thenReturn(staffResults);

        List<MoeStaff> staffList = new ArrayList<>();
        var moeStaff1 = new MoeStaff();
        moeStaff1.setId(1);
        moeStaff1.setSort(15);
        staffList.add(moeStaff1);
        var moeStaff3 = new MoeStaff();
        moeStaff3.setId(3);
        moeStaff3.setSort(15);
        staffList.add(moeStaff3);
        var moeStaff5 = new MoeStaff();
        moeStaff5.setId(5);
        moeStaff5.setSort(15);
        staffList.add(moeStaff5);
        // 创建一个新的 mock 对象来代表 useDataSource 的返回值
        MoeStaffMapper readerMapper = mock(MoeStaffMapper.class);
        when(moeStaffMapper.useDataSource(READER)).thenReturn(readerMapper);
        when(readerMapper.queryStaffByIdListV2(staffIdList)).thenReturn(staffList);

        // 执行测试
        List<StaffWorkingRangeDto> result =
                workingDailyService.simpleQueryStaffWorkTime(businessId, tokenStaffId, rangeVo);

        // 验证结果：相同 sort 值时按 staffId 升序排列
        assertNotNull(result);
        assertEquals(3, result.size());

        // 期望顺序：staffId=1, staffId=3, staffId=5
        assertEquals(1, result.get(0).getStaffId());
        assertEquals(3, result.get(1).getStaffId());
        assertEquals(5, result.get(2).getStaffId());

        // 验证所有员工的 sort 值都相同
        assertEquals(15, result.get(0).getSort());
        assertEquals(15, result.get(1).getSort());
        assertEquals(15, result.get(2).getSort());
    }

    /**
     * 测试空员工列表的情况
     */
    @Test
    void testSimpleQueryStaffWorkTime_EmptyStaffMap() {
        // 准备测试数据
        int businessId = 1;
        Integer tokenStaffId = 100;
        var staffIdList = List.of(1, 2, 3);
        WorkingDailyQueryRangeVo rangeVo = new WorkingDailyQueryRangeVo()
                .setStartDate("2025-01-01")
                .setEndDate("2025-01-07")
                .setStaffIdList(staffIdList);

        List<StaffByWorkingDateVO> staffResults = new ArrayList<>();
        when(staffWorkingHourService.getStaffListByWorkingDateAndStaffIdList(
                        businessId, staffIdList, "2025-01-01", "2025-01-07"))
                .thenReturn(staffResults);

        // 执行测试
        List<StaffWorkingRangeDto> result =
                workingDailyService.simpleQueryStaffWorkTime(businessId, tokenStaffId, rangeVo);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
