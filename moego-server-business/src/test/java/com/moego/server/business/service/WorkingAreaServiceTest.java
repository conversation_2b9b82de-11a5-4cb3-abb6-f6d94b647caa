package com.moego.server.business.service;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.moego.common.utils.DateUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.server.business.dto.StaffOverrideAreaDetailDTO;
import com.moego.server.business.dto.StaffWorkingAreaDetailDTO;
import com.moego.server.business.dto.StaffWorkingAreaRangeDto;
import com.moego.server.business.dto.StaffWorkingHourDetailDTO;
import com.moego.server.business.dto.WorkingAreaDto;
import com.moego.server.business.mapper.MoeGeoareaMapper;
import com.moego.server.business.mapper.MoeStaffServiceAreaMapper;
import com.moego.server.business.mapperbean.MoeGeoarea;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.mapperbean.MoeStaffServiceArea;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WorkingAreaServiceTest {

    @Mock
    MoeGeoareaMapper moeGeoareaMapper;

    @Mock
    StaffWorkingAreaService staffWorkingAreaService;

    @Mock
    StaffOverrideAreaService staffOverrideAreaService;

    @Mock
    WorkingDailyService workingDailyService;

    @Mock
    StaffService staffService;

    @Mock
    MigrateHelper migrateHelper;

    @Mock
    StaffWorkingHourService staffWorkingHourService;

    @Mock
    MoeStaffServiceAreaMapper moeStaffServiceAreaMapper;

    @InjectMocks
    WorkingAreaService workingAreaService;

    @Test
    void getAreaIdsFromWorkingArea() {
        StaffWorkingAreaRangeDto dto1 = new StaffWorkingAreaRangeDto();
        dto1.setStaffId(1);
        dto1.setWorkingAreaRange(Map.of("2023-02-01", List.of(new WorkingAreaDto(1))));
        StaffWorkingAreaRangeDto dto2 = new StaffWorkingAreaRangeDto();
        dto2.setStaffId(1);
        dto2.setWorkingAreaRange(
                Map.of("2023-02-02", List.of(new WorkingAreaDto(2)), "2023-02-05", List.of(new WorkingAreaDto(1))));
        List<StaffWorkingAreaRangeDto> staffWorkingAreaRangeDtos = List.of(dto1, dto2);
        assertArrayEquals(
                new Integer[] {1, 2},
                workingAreaService
                        .getAreaIdsFromWorkingArea(staffWorkingAreaRangeDtos)
                        .toArray());
    }

    @Test
    void areaIdDeleteCheck() {
        MoeGeoarea area1 = new MoeGeoarea();
        area1.setId(1);
        MoeGeoarea area2 = new MoeGeoarea();
        area2.setId(2);
        when(moeGeoareaMapper.queryAreasNotDelete(any(), any(), any())).thenReturn(List.of(area1, area2));

        List<WorkingAreaDto> workingAreaDtos = List.of(
                new WorkingAreaDto(-1),
                new WorkingAreaDto(1),
                new WorkingAreaDto(1),
                new WorkingAreaDto(2),
                new WorkingAreaDto(3));

        workingAreaService.areaIdDeleteCheck(1000, workingAreaDtos);
        assertEquals(-1, workingAreaDtos.get(0).getAreaId());
        assertEquals(1, workingAreaDtos.get(1).getAreaId());
        assertEquals(-1, workingAreaDtos.get(4).getAreaId());
    }

    @Test
    void queryStaffWorkAreaByRange() {
        String startDate = "2023-10-05";
        String endDate = "2023-10-25";
        Integer staffId1 = 10;
        Integer staffId2 = 20;
        Map<LocalDate, List<WorkingAreaDto>> staff1WorkingArea =
                DateUtil.generateAllDatesBetween("2023-10-07", "2023-10-23").stream()
                        .collect(Collectors.toMap(
                                LocalDate::parse, k -> List.of(new WorkingAreaDto(-1)), (k1, k2) -> k1));
        staff1WorkingArea.put(LocalDate.parse("2023-10-07"), List.of(new WorkingAreaDto(6)));
        staff1WorkingArea.put(LocalDate.parse("2023-10-09"), List.of(new WorkingAreaDto(6)));
        Map<LocalDate, List<WorkingAreaDto>> staff2WorkingArea =
                DateUtil.generateAllDatesBetween("2023-10-07", "2023-10-23").stream()
                        .collect(Collectors.toMap(
                                LocalDate::parse, k -> List.of(new WorkingAreaDto(-1)), (k1, k2) -> k1));
        staff2WorkingArea.put(LocalDate.parse("2023-10-17"), List.of(new WorkingAreaDto(5)));
        staff2WorkingArea.put(LocalDate.parse("2023-10-19"), List.of(new WorkingAreaDto(5)));
        when(staffWorkingAreaService.getStaffWorkingArea(any(), any(), any()))
                .thenReturn(Map.of(staffId1, staff1WorkingArea, staffId2, staff2WorkingArea));
        when(staffOverrideAreaService.getStaffOverrideWorkingAreaMap(any(), any(), any(), any()))
                .thenReturn(Map.of(
                        Pair.of(staffId1, LocalDate.parse("2023-10-07")),
                        List.of(new WorkingAreaDto(16)),
                        Pair.of(staffId1, LocalDate.parse("2023-10-21")),
                        List.of(new WorkingAreaDto(16)),
                        Pair.of(staffId2, LocalDate.parse("2023-10-17")),
                        List.of(new WorkingAreaDto(15)),
                        Pair.of(staffId2, LocalDate.parse("2023-10-21")),
                        List.of(new WorkingAreaDto(15))));
        List<StaffWorkingAreaRangeDto> result = workingAreaService.queryStaffWorkAreaByRange(
                1000, List.of(staffId1, staffId2), startDate, endDate, true);
        for (StaffWorkingAreaRangeDto dto : result) {
            if (dto.getStaffId().equals(staffId1)) {
                assertEquals(dto.getWorkingAreaRange().get("2023-10-05").get(0).getAreaId(), -1);
                assertEquals(dto.getWorkingAreaRange().get("2023-10-25").get(0).getAreaId(), -1);
                assertEquals(dto.getWorkingAreaRange().get("2023-10-07").get(0).getAreaId(), 16);
                assertEquals(dto.getWorkingAreaRange().get("2023-10-09").get(0).getAreaId(), 6);
                assertEquals(dto.getWorkingAreaRange().get("2023-10-21").get(0).getAreaId(), 16);
            }
            if (dto.getStaffId().equals(staffId2)) {
                assertEquals(dto.getWorkingAreaRange().get("2023-10-05").get(0).getAreaId(), -1);
                assertEquals(dto.getWorkingAreaRange().get("2023-10-25").get(0).getAreaId(), -1);
                assertEquals(dto.getWorkingAreaRange().get("2023-10-17").get(0).getAreaId(), 15);
                assertEquals(dto.getWorkingAreaRange().get("2023-10-19").get(0).getAreaId(), 5);
                assertEquals(dto.getWorkingAreaRange().get("2023-10-21").get(0).getAreaId(), 15);
            }
        }
    }

    @Test
    void transToShiftManagement() {
        Integer companyId = 1000;
        Integer businessId = 100;
        Integer staffId = 10;
        WorkingAreaService spyWorkingAreaService = spy(workingAreaService);
        when(migrateHelper.getMigrationInfo(100)).thenReturn(new MigrateInfo(1000, false));
        when(staffService.getStaffListByBusinessId(businessId, false, new MigrateInfo(1000, false)))
                .thenReturn(List.of(new MoeStaff() {
                    {
                        setId(staffId);
                    }
                }));
        StaffWorkingHourDetailDTO staffWorkingHourDetailDTO = new StaffWorkingHourDetailDTO();
        when(staffWorkingHourService.getStaffWorkingHourDetail(businessId, List.of(staffId)))
                .thenReturn(Map.of(staffId, staffWorkingHourDetailDTO));
        when(moeStaffServiceAreaMapper.queryByBusinessNotDelete(any(), any(), any(), any()))
                .thenReturn(List.of());
        doNothing().when(staffWorkingAreaService).saveStaffWorkingArea(any(), any(), any());
        doNothing().when(staffOverrideAreaService).saveStaffOverrideArea(any(), any(), any());
        doReturn(null).when(spyWorkingAreaService).transPerStaffToShiftManagement(any(), any(), any());

        spyWorkingAreaService.transToShiftManagement(companyId, businessId);
    }

    @Test
    void transPerStaffToShiftManagement() {
        Integer businessId = 1000;
        Integer staffId = 10;

        List<MoeStaffServiceArea> staffDateServiceAreas = List.of(
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-10-01"); // 历史数据，不迁移
                        setAreaId(1);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-11-01"); // 周三
                        setAreaId(1);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-11-08"); // 周三
                        setAreaId(1);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-11-15"); // 周三
                        setAreaId(2);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-11-22"); // 周三
                        setAreaId(3);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-12-03"); // 周三，超出绝对结束时间，迁移为 override
                        setAreaId(1);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-11-02"); // 周四
                        setAreaId(11);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-11-09"); // 周四
                        setAreaId(12);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-11-16"); // 周四
                        setAreaId(11);
                    }
                },
                new MoeStaffServiceArea() {
                    {
                        setStaffId(staffId);
                        setDate("2023-11-23"); // 周四
                        setAreaId(11);
                    }
                });

        StaffWorkingHourDetailDTO staffWorkingHourDetailDTO = new StaffWorkingHourDetailDTO() {
            {
                setBusinessId(businessId);
                setStaffId(staffId);
                setStartDate(LocalDate.parse("2023-11-01"));
                setEndDate(LocalDate.parse("2023-12-02"));
                setScheduleType((byte) 1);
            }
        };
        LocalDate startDate = LocalDate.parse("2023-11-01");
        Pair<StaffWorkingAreaDetailDTO, List<StaffOverrideAreaDetailDTO>> result =
                workingAreaService.transPerStaffToShiftManagement(
                        staffWorkingHourDetailDTO, staffDateServiceAreas, startDate);
        // 校验 StaffWorkingAreaDetail
        for (int weekNumber = 0; weekNumber < 4; weekNumber++) {
            for (int dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
                if (weekNumber == 0 && dayOfWeek == 3) {
                    assertEquals(
                            1,
                            StaffWorkingAreaService.getStaffWorkingAreaByWeekAndDayNumber(
                                            weekNumber, dayOfWeek, result.getLeft())
                                    .get(0)
                                    .getAreaId());
                } else if (weekNumber == 0 && dayOfWeek == 4) {
                    assertEquals(
                            11,
                            StaffWorkingAreaService.getStaffWorkingAreaByWeekAndDayNumber(
                                            weekNumber, dayOfWeek, result.getLeft())
                                    .get(0)
                                    .getAreaId());
                } else {
                    assertEquals(
                            -1,
                            StaffWorkingAreaService.getStaffWorkingAreaByWeekAndDayNumber(
                                            weekNumber, dayOfWeek, result.getLeft())
                                    .get(0)
                                    .getAreaId());
                }
            }
        }
        // 校验 StaffOverrideAreaDetail
        assertEquals(6, result.getRight().size());

        staffWorkingHourDetailDTO.setScheduleType((byte) 2);
        result = workingAreaService.transPerStaffToShiftManagement(
                staffWorkingHourDetailDTO, staffDateServiceAreas, startDate);
        for (int weekNumber = 0; weekNumber < 4; weekNumber++) {
            for (int dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
                if (weekNumber == 0 && dayOfWeek == 4) {
                    assertEquals(
                            11,
                            StaffWorkingAreaService.getStaffWorkingAreaByWeekAndDayNumber(
                                            weekNumber, dayOfWeek, result.getLeft())
                                    .get(0)
                                    .getAreaId());
                } else {
                    assertEquals(
                            -1,
                            StaffWorkingAreaService.getStaffWorkingAreaByWeekAndDayNumber(
                                            weekNumber, dayOfWeek, result.getLeft())
                                    .get(0)
                                    .getAreaId());
                }
            }
        }
        // 校验 StaffOverrideAreaDetail
        assertEquals(9, result.getRight().size());
    }
}
