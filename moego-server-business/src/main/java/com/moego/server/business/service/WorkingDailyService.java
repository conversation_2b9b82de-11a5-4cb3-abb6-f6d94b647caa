package com.moego.server.business.service;

import static com.moego.server.business.common.consts.DataSourceConst.READER;

import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.moego.common.dto.TimePeriodDto;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.MoeWorkingDailyDTO;
import com.moego.server.business.dto.StaffWorkingHourDayDetailDTO;
import com.moego.server.business.dto.StaffWorkingHourDetailDTO;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.dto.WorkingDailySaveVo;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.business.web.vo.StaffByWorkingDateVO;
import com.moego.server.business.web.vo.StaffWithWorkingRangeVO;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.params.UpdateStaffTimeParam;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class WorkingDailyService {

    @Autowired
    private IGroomingOnlineBookingClient groomingOnlineBookingClient;

    @Autowired
    private StaffService staffService;

    @Autowired
    private StaffWorkingHourService staffWorkingHourService;

    @Autowired
    private MoeStaffMapper moeStaffMapper;

    private static final Map<Byte, Function<StaffWorkingHourDayDetailDTO, List<TimeRangeDto>>>
            DAY_OF_WEEK_FOR_GET_STAFF = ImmutableMap.of(
                    (byte) 0,
                    StaffWorkingHourDayDetailDTO::getSunday,
                    (byte) 1,
                    StaffWorkingHourDayDetailDTO::getMonday,
                    (byte) 2,
                    StaffWorkingHourDayDetailDTO::getTuesday,
                    (byte) 3,
                    StaffWorkingHourDayDetailDTO::getWednesday,
                    (byte) 4,
                    StaffWorkingHourDayDetailDTO::getThursday,
                    (byte) 5,
                    StaffWorkingHourDayDetailDTO::getFriday,
                    (byte) 6,
                    StaffWorkingHourDayDetailDTO::getSaturday);

    /**
     * 只考虑working hour的内容
     *
     * @param businessId
     * @param workingDailySaveVo
     * @return
     */
    @Transactional
    public Integer insertOrUpdate(Long companyId, Integer businessId, WorkingDailySaveVo workingDailySaveVo) {
        if (staffService.getCompanyStaff(companyId, workingDailySaveVo.getStaffId()) == null) {
            return 0;
        }
        updateStaffAvailableTimeByWorkingTime(businessId, workingDailySaveVo);

        StaffWorkingHourDetailDTO staffWorkingHourDetail = staffWorkingHourService.getStaffWorkingHourDetail(
                companyId, businessId, workingDailySaveVo.getStaffId());
        StaffWorkingHourDayDetailDTO dayDetail = staffWorkingHourDetail.getFirstWeek();
        // 设置周期内某一天的 working hour 数据
        StaffWorkingHourService.DAY_OF_WEEK_FOR_SET_STAFF.forEach((dayOfWeek, consumer) -> {
            if (Objects.equals(dayOfWeek, workingDailySaveVo.getDayOfWeek())) {
                List<TimeRangeDto> timeData = JsonUtil.toList(workingDailySaveVo.getTimeRange(), TimeRangeDto.class);
                consumer.accept(dayDetail, timeData);
            }
        });
        staffWorkingHourDetail.setFirstWeek(dayDetail);
        staffWorkingHourService.saveStaffWorkingHour(companyId, staffWorkingHourDetail);

        return 1;
    }

    private void updateStaffAvailableTimeByWorkingTime(Integer businessId, WorkingDailySaveVo workingDailySaveVo) {
        UpdateStaffTimeParam updateStaffTimeParam = new UpdateStaffTimeParam();
        updateStaffTimeParam.setBusinessId(businessId);
        updateStaffTimeParam.setDayOfWeek(workingDailySaveVo.getDayOfWeek());
        updateStaffTimeParam.setStaffId(workingDailySaveVo.getStaffId());
        updateStaffTimeParam.setTimeRange(new Gson()
                .fromJson(workingDailySaveVo.getTimeRange(), new TypeToken<List<TimePeriodDto>>() {}.getType()));
        if (updateStaffTimeParam.getTimeRange() == null
                && updateStaffTimeParam.getTimeRange().size() == 0) {
            updateStaffTimeParam.setIsClose(true);
        }
        ThreadPool.execute(
                () -> groomingOnlineBookingClient.updateStaffAvailableTimeByWorkingTime(updateStaffTimeParam));
    }

    @Deprecated
    public List<MoeWorkingDailyDTO> query(MoeWorkingDailyDTO moeWorkingDailyDTO) {
        Integer businessId = moeWorkingDailyDTO.getBusinessId();
        Integer staffId = moeWorkingDailyDTO.getStaffId();
        // MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        // if (staffService.getCompanyStaff(migrateInfo.companyId(), staffId) == null) {
        //     return Collections.emptyList();
        // }
        //
        // StaffWorkingHourDetailDTO staffWorkingHourDetail =
        //         staffWorkingHourService.getStaffWorkingHourDetail(migrateInfo.companyId(), businessId, staffId);
        // StaffWorkingHourDayDetailDTO firstWeek = staffWorkingHourDetail.getFirstWeek();

        return DAY_OF_WEEK_FOR_GET_STAFF.entrySet().stream()
                .map(entry -> {
                    MoeWorkingDailyDTO dto = new MoeWorkingDailyDTO();
                    dto.setDayOfWeek(entry.getKey());
                    // List<TimeRangeDto> time = entry.getValue().apply(firstWeek);
                    dto.setTimeRange(JsonUtil.toJson(List.of()));
                    dto.setBusinessId(businessId);
                    dto.setStaffId(staffId);
                    return dto;
                })
                .toList();
    }

    public List<StaffWorkingRangeDto> simpleQueryStaffWorkTime(
            int businessId, Integer tokenStaffId, WorkingDailyQueryRangeVo rangeVo) {
        // 参数检查
        String startDateRequest = rangeVo.getStartDate();
        String endDateRequest = rangeVo.getEndDate();
        if (StringUtils.isEmpty(endDateRequest) || StringUtils.isEmpty(startDateRequest)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "date is null or empty");
        }

        var staffIdList = rangeVo.getStaffIdList();
        if (CollectionUtils.isEmpty(staffIdList)) {
            var staffMap = staffService.getShowOnCalendarStaffMap(businessId, tokenStaffId);
            staffIdList = staffMap.keySet().stream().toList();
        }

        List<StaffByWorkingDateVO> staffResults = staffWorkingHourService.getStaffListByWorkingDateAndStaffIdList(
                businessId, staffIdList, startDateRequest, endDateRequest);

        if (CollectionUtils.isEmpty(staffResults)) {
            return List.of();
        }

        // get staff ids
        List<Integer> staffIds = staffResults.stream()
                .map(StaffByWorkingDateVO::getStaffs)
                .flatMap(Collection::stream)
                .map(StaffWithWorkingRangeVO::getStaffId)
                .distinct()
                .toList();

        var staffDetailMap = moeStaffMapper.useDataSource(READER).queryStaffByIdListV2(staffIds).stream()
                .collect(Collectors.toMap(MoeStaff::getId, Function.identity()));

        Map<Integer, StaffWorkingRangeDto> result = new HashMap<>();
        staffResults.forEach(vo -> vo.getStaffs().forEach(rangeVO -> {
            var staffId = rangeVO.getStaffId();
            var rangeDto = result.get(staffId);
            if (Objects.isNull(rangeDto)) {
                rangeDto = new StaffWorkingRangeDto();
                rangeDto.setStaffId(staffId);
                var staffDetail = staffDetailMap.getOrDefault(staffId, new MoeStaff());
                rangeDto.setFirstName(staffDetail.getFirstName());
                rangeDto.setLastName(staffDetail.getLastName());
                rangeDto.setColorCode("#FF0000");
                rangeDto.setSort(staffDetail.getSort());
                rangeDto.setTimeRange(new HashMap<>());
            }
            rangeDto.getTimeRange().put(vo.getDate(), rangeVO.getTimeRange());
            result.put(staffId, rangeDto);
        }));
        return result.values().stream()
                .sorted(Comparator.comparing(StaffWorkingRangeDto::getSort, Comparator.reverseOrder())
                        .thenComparing(StaffWorkingRangeDto::getStaffId))
                .toList();
    }

    public List<StaffWorkingRangeDto> queryRangeShiftManagementWorkTime(
            Integer businessId, String startDateRequest, String endDateRequest) {
        // 参数检查
        if (StringUtils.isEmpty(endDateRequest) || StringUtils.isEmpty(startDateRequest)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "date is null or empty");
        }

        // 查询需要获取时间的员工列表
        Map<Integer, MoeStaffDto> staffMap = staffService.getCurrentWorkingLocationStaffMap(businessId);
        if (CollectionUtils.isEmpty(staffMap)) {
            return Collections.emptyList();
        }

        WorkingDailyQueryRangeVo vo = new WorkingDailyQueryRangeVo();
        vo.setStartDate(startDateRequest);
        vo.setEndDate(endDateRequest);
        vo.setStaffIdList(staffMap.keySet().stream().toList());
        return simpleQueryStaffWorkTime(businessId, null, vo);

        // return queryStaffWorkTimeByRange(
        //                 businessId, new ArrayList<>(staffMap.keySet()), startDateRequest, endDateRequest)
        //         .entrySet()
        //         .stream()
        //         .map(entry -> makeStaffWorkingRangeDto(staffMap.get(entry.getKey()), entry.getValue()))
        //         .sorted(Comparator.comparing(StaffWorkingRangeDto::getSort, Comparator.reverseOrder())
        //                 .thenComparing(StaffWorkingRangeDto::getStaffId))
        //         .toList();
    }

    public StaffWorkingRangeDto makeStaffWorkingRangeDto(
            MoeStaffDto staff, Map<String, List<TimeRangeDto>> allDayOfWeekWorkingTime) {
        StaffWorkingRangeDto staffRangeDto = new StaffWorkingRangeDto();
        staffRangeDto.setStaffId(staff.getId());
        staffRangeDto.setFirstName(staff.getFirstName());
        staffRangeDto.setLastName(staff.getLastName());
        staffRangeDto.setSort(staff.getSort());
        staffRangeDto.setColorCode("#FF0000");
        staffRangeDto.setTimeRange(allDayOfWeekWorkingTime);
        return staffRangeDto;
    }

    public Map<Integer, Map<String, List<TimeRangeDto>>> queryStaffWorkTimeByRange(
            int businessId, List<Integer> staffIdList, String startDate, String endDate) {

        return staffWorkingHourService.getStaffWithOverrideDateAndClosedDateString(
                businessId, staffIdList, startDate, endDate);

        // startDate = DateUtil.convertToDateString(startDate);
        // endDate = DateUtil.convertToDateString(endDate);
        //
        // // 日期转换为一段时间
        // List<LocalDate> localDateRange = getLocalDateRange(LocalDate.parse(startDate), LocalDate.parse(endDate));
        //
        // // 获取business的closed date & 提前查出来那些date不可用
        // List<MoeBusinessCloseDate> allCloseDate =
        //         closeDateService.getCloseDateByStartDateEndDate(businessId, startDate, endDate);
        // List<LocalDate> noAvailableDate = closeDateService.getNoAvailableDate(localDateRange, allCloseDate);
        //
        // // 获取 override 设置
        // Map<Pair<Integer, LocalDate>, List<TimeRangeDto>> staffOverrideDetailMap =
        //         staffOverrideDateService.getStaffOverrideDateDetailMap(businessId, startDate, endDate);
        //
        // // 获取 working hour 设置
        // Map<Integer, Map<LocalDate, List<TimeRangeDto>>> staffWorkingHour =
        //         staffWorkingHourService.getStaffWorkingHour(businessId, staffIdList, localDateRange);
        //
        // // 开始计算&组装返回值
        // Map<Integer, Map<String, List<TimeRangeDto>>> result = new HashMap<>();
        // staffWorkingHour.forEach((staffId, allDayOfWeekWorkingTime) -> {
        //     // 组装所有的date
        //     Map<String, List<TimeRangeDto>> timeRangeMap = new HashMap<>(32);
        //     localDateRange.forEach(localDate -> {
        //         String dateStr = localDate.toString();
        //         if (!CollectionUtils.isEmpty(noAvailableDate) && noAvailableDate.contains(localDate)) {
        //             // 使用 closed localDate 或 holiday 覆盖
        //             timeRangeMap.put(dateStr, Collections.emptyList());
        //         } else if (!CollectionUtils.isEmpty(staffOverrideDetailMap)
        //                 && staffOverrideDetailMap.containsKey(Pair.of(staffId, localDate))) {
        //             // 使用 override localDate 覆盖
        //             timeRangeMap.put(dateStr, staffOverrideDetailMap.get(Pair.of(staffId, localDate)));
        //         } else {
        //             timeRangeMap.put(dateStr, allDayOfWeekWorkingTime.get(localDate));
        //         }
        //     });
        //     result.put(staffId, timeRangeMap);
        // });
        // return result;
    }

    // 日期转换为一段时间
    static List<LocalDate> getLocalDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate.isAfter(endDate)) {
            return Collections.emptyList();
        }
        return Stream.iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .toList();
    }

    /**
     * @param businessId
     * @param staffIds
     * @return map<weekDayIndex, map<staffId, timeRanges>>
     */
    public Map<Integer, Map<Integer, TimeRangeDto>> getBusinessStaffWorkingHour(
            Integer businessId, List<Integer> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Map.of();
        }

        Map<Integer, StaffWorkingHourDetailDTO> staffWorkingHourDetail =
                staffWorkingHourService.getStaffWorkingHourDetail(businessId, staffIds);
        if (CollectionUtils.isEmpty(staffWorkingHourDetail)) {
            return Map.of();
        }

        return DAY_OF_WEEK_FOR_GET_STAFF.keySet().stream()
                .collect(Collectors.toMap(
                        Byte::intValue,
                        dayOfWeek -> staffWorkingHourDetail.keySet().stream()
                                .collect(Collectors.toMap(Function.identity(), staffId -> {
                                    StaffWorkingHourDetailDTO staffWorkingHourDetailDTO =
                                            staffWorkingHourDetail.get(staffId);
                                    if (staffWorkingHourDetailDTO != null
                                            && staffWorkingHourDetailDTO.getFirstWeek() != null) {
                                        StaffWorkingHourDayDetailDTO firstWeek =
                                                staffWorkingHourDetailDTO.getFirstWeek();
                                        List<TimeRangeDto> tr = DAY_OF_WEEK_FOR_GET_STAFF
                                                .get(dayOfWeek)
                                                .apply(firstWeek);
                                        if (!CollectionUtils.isEmpty(tr)) {
                                            return tr.get(0);
                                        }
                                    }
                                    return new TimeRangeDto();
                                })),
                        (v1, v2) -> v1));
    }
}
