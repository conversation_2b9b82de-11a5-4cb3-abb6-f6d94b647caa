plugins {
    id 'org.springframework.boot' version "${springBootVersion}" apply false
    id 'io.spring.dependency-management' version "${springDependencyManagementVersion}" apply false
    id 'com.diffplug.spotless' version "${spotlessVersion}" apply false
    id "com.github.spotbugs" version "${spotbugsVersion}" apply false
    id 'jacoco'
    id 'net.razvan.jacoco-to-cobertura' version "${jacocoToCoberturaPlugin}"
}

allprojects {
    apply plugin: 'java'
    apply plugin: 'java-library'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'com.diffplug.spotless'
    apply plugin: 'com.github.spotbugs'
    apply plugin: 'jacoco'

    repositories {
        mavenLocal()
        mavenCentral()
    }

    compileJava {
        options.encoding = 'UTF-8'
        options.compilerArgs << '-parameters'
    }

    compileTestJava {
        options.encoding = 'UTF-8'
        options.compilerArgs << '-parameters'
    }

    test {
        useJUnitPlatform()
    }

    // dependency management
    dependencyManagement {
        imports {
            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        }
    }

    // spotless
    spotless {
        encoding 'UTF-8'
        java {
            toggleOffOn()
            removeUnusedImports()
            trimTrailingWhitespace()
            endWithNewline()
            palantirJavaFormat()

            targetExclude "build/generated/**"
            custom('Refuse wildcard imports', {
                if (it =~ /\nimport .*\*;/) {
                    throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
                }
            } as Closure<String>)
        }
    }

    // spotbugs
    spotbugs {
        spotbugsTest.enabled = false
        omitVisitors.addAll 'FindReturnRef', 'MethodReturnCheck', 'SerializableIdiom'
        excludeFilter = file("${rootDir}/config/spotbugs/exclude.xml")
    }

    jacoco {
        toolVersion = "0.8.8"
    }

    test {
        useJUnitPlatform()

        finalizedBy jacocoTestReport
        jacoco {
            destinationFile = file("$buildDir/jacoco/test.exec")
        }

        reports {
            junitXml.required = true
            junitXml.outputLocation = file("${rootProject.buildDir}/test-results/test")
        }
    }

    jacocoTestReport {
        dependsOn test
        reports {
            xml.required = true
            xml.outputLocation = file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
        }
        afterEvaluate {
            classDirectories.setFrom(files(classDirectories.files.collect {
                fileTree(dir: it,
                        include: ["**/service/*.class", "**/service/util/**", "**/converter/**"])
            }))
        }
    }

    jacocoToCobertura {
        inputFile = file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
        outputFile = file("${rootProject.buildDir}/reports/jacoco/test/cobertura-jacocoTestReport.xml")
    }

    jacocoToCobertura.dependsOn jacocoTestReport
}
