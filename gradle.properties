group=com.moego
artifact=moego-server-customer
version=1.0.0

# Spring related
springBootVersion=3.1.4
springCloudVersion=2022.0.4
springDependencyManagementVersion=1.1.3

# https://github.com/awspring/spring-cloud-aws/blob/v3.2.1/spring-cloud-aws-dependencies/pom.xml
# è¿ä¸ªæå¡ä¾èµäº lib-awsï¼å»ºè®®æ¯ä¸è¦ç¨ï¼ï¼ä½¿ç¨ççæ¬å springCloudAWS ä½¿ç¨ççæ¬æå²çªï¼springCloudAWS ä½¿ç¨ççæ¬è¾ä½ï¼
# è¿éç¨ springCloudAWS 3.2.1 å¯¹åºç awssdk çæ¬
awsSdkVersion=2.25.70
# https://github.com/awspring/spring-cloud-aws?tab=readme-ov-file#compatibility-with-spring-project-versions
springCloudAWSVersion=3.0.5

mybatisBootStarterVersion=3.0.2
mybatisGeneratorCoreVersion=1.4.2
# https://plugins.gradle.org/plugin/com.qqviaja.gradle.MybatisGenerator
mybatisGeneratorGradlePlugin=2.5
pagehelperSpringBootStarterVersion=2.0.0
jacocoToCoberturaPlugin=1.2.0

grpcVersion=1.58.0
mapstructVersion=1.5.5.Final

# Code quality
spotlessVersion=6.22.0
spotbugsVersion=5.1.3

org.gradle.jvmargs=-Xmx3g
org.gradle.caching=true
org.gradle.cache.lockTimeout=1200000
